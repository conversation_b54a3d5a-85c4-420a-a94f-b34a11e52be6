{"name": "cadetlabs", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "start": "turbo run start", "lint": "turbo run lint", "check-types": "turbo run check-types", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@types/node": "^24.5.2", "prettier": "^3.6.0", "prettier-plugin-tailwindcss": "^0.6.11", "turbo": "^2.5.6", "typescript": "^5.9.2"}, "packageManager": "pnpm@8.15.6", "engines": {"node": ">=18"}, "dependencies": {"@azure/storage-blob": "^12.28.0", "@tanstack/react-table": "^8.21.3", "@types/express": "^5.0.3", "busboy": "^1.6.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dayjs": "^1.11.18", "express": "^5.1.0", "helmet": "^8.1.0", "http-status-codes": "^2.3.0", "jotai": "^2.14.0", "morgan": "^1.10.1", "react-hook-form": "^7.62.0", "reflect-metadata": "^0.2.2", "supertokens-node": "^23.0.1"}}