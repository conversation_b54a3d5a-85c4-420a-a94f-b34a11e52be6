import Session from "supertokens-node/recipe/session";
import { Request, Response, NextFunction } from "express";

/**
 * Method decorator to protect routes/methods with SuperTokens session verification.
 * Usage: @Protected
 */
export function Protected(
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) {
  const originalMethod = descriptor.value;

  descriptor.value = async function (req: Request, res: Response, ...args: any[]) {
    try {
      // Verify session using SuperTokens
      const session = await Session.getSession(req, res);
      if (!session) {
        return res.status(401).json({ error: "Unauthorized" });
      }
      // Pass session as a new argument downstream
      return await originalMethod.apply(this, [req, res, session, ...args]);
    } catch (err) {
      return res.status(401).json({ error: "Unauthorized" });
    }
  };
}
