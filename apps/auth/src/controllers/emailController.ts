import { Request, Response } from "express";
import EmailPassword from "supertokens-node/recipe/emailpassword";
import EmailVerification from "supertokens-node/recipe/emailverification";
import fs from "fs";
import { listUsersByAccountInfo, User } from "supertokens-node";
import supertokens from "supertokens-node";
import { EmailService } from "../services/emailService.js";
const TENANT_ID = process.env.SUPERTOKENS_TENANT_ID || "public";
const WEBSITE_DOMAIN = process.env.WEBSITE_DOMAIN || "http://localhost:3000";
import { log } from "@repo/logger";
import { getSession } from "supertokens-node/recipe/session";

export class EmailController {
  /**
   * Send email verification to authenticated user
   */

  static async sendEmailVerificationToUserByEmail(email: string) {
    const users: User[] = await listUsersByAccountInfo(TENANT_ID, { email: email });
    if (!users || users.length === 0) {
      return;
    }
    const user = users[0];
    let recipeUserId;
    if (user.loginMethods && user.loginMethods.length > 0 && user.loginMethods[0].recipeUserId) {
      recipeUserId = user.loginMethods[0].recipeUserId;
    } else {
      // create one from the primary user id
      recipeUserId = new supertokens.RecipeUserId(user.id);
    }
    const tokenResp = await EmailVerification.createEmailVerificationToken(
      TENANT_ID,
      recipeUserId,
      email
    );
    if (tokenResp.status !== "OK") {
      return;
    }

    const token = tokenResp.token;
    const verificationLink = `${WEBSITE_DOMAIN}/auth/verify-email?token=${encodeURIComponent(token)}&rid=emailverification`;
    console.log("verificationLink:", verificationLink);
    EmailService.sendEmailWithTemplate(
      {
        to: email,
        subject: "Please verify your email",
        data: {
          appName: "CadetLabs",
          verificationLink,
        },
      },
      "email-verify"
    );
  }

  static async sendEmailVerification(req: Request, res: Response) {
    try {
      const emailBody = req.body.email;
      const users = await listUsersByAccountInfo(TENANT_ID, { email: emailBody });
      if (!users || users.length === 0) {
        return res.json({
          status: "OK",
          note: "If the email exists, a reset message will be sent.",
        });
      }

      const user = users[0];
      const email = user?.emails?.[0];
      if (!email) return res.status(400).json({ error: "User has no email to verify" });

      // --- IMPORTANT: ensure we pass a RecipeUserId (not a string) ---
      let recipeUserId;
      if (user.loginMethods && user.loginMethods.length > 0 && user.loginMethods[0].recipeUserId) {
        recipeUserId = user.loginMethods[0].recipeUserId; // already a RecipeUserId if returned by SDK
      } else {
        // create one from the primary user id
        recipeUserId = new supertokens.RecipeUserId(user.id);
      }

      // create token (note the tenantId, then recipeUserId, optionally email)
      const tokenResp = await EmailVerification.createEmailVerificationToken(
        TENANT_ID,
        recipeUserId,
        email
      );
      if (tokenResp.status !== "OK") {
        return res.status(400).json({ status: tokenResp.status });
      }

      const token = tokenResp.token;
      const verificationLink = `${WEBSITE_DOMAIN}/auth/verify-email?token=${encodeURIComponent(token)}&rid=emailverification`;
      console.log("verificationLink:", verificationLink);
      EmailService.sendEmailWithTemplate(
        {
          to: email,
          subject: "Please verify your email",
          data: {
            appName: "CadetLabs",
            verificationLink,
          },
        },
        "email-verify"
      );

      return res.json({ status: "OK" });
    } catch (err) {
      console.error("email verify send error:", err);
      return res.status(500).json({ error: "Failed to send verification email" });
    }
  }

  /**
   * Verify email using verification token
   */
  static async verifyEmailToken(req: Request, res: Response) {
    try {
      const { token } = req.body ?? {};
      if (!token) return res.status(400).json({ error: "token is required" });

      const resp = await EmailVerification.verifyEmailUsingToken(TENANT_ID, token);
      if (resp.status !== "OK" || !resp.user) {
        return res.status(200).json({ status: resp.status });
      }

      // Correct way to access the user ID
      const userId = resp.user.recipeUserId.getAsString();
      const email = resp.user.email;
      console.log("email verify token userId:", userId);
      console.log("email verify token email:", email);
      // Now create reset password token
      const tokenResp = await EmailPassword.createResetPasswordToken(TENANT_ID, userId, email);
      console.log("createResetPasswordToken:", tokenResp);
      if (tokenResp.status !== "OK") {
        console.error("createResetPasswordToken:", tokenResp);
        return res.status(500).json({ status: tokenResp.status });
      }

      return res.json({
        status: resp.status,
        resetToken: tokenResp.token,
      });
    } catch (err) {
      console.error("email verify token error:", err);
      return res.status(500).json({ error: "Failed to verify email token" });
    }
  }

  /**
   * Check email verification status
   */
  static async checkEmailVerificationStatus(req: Request, res: Response) {
    try {
      // ✅ get session directly from req/res
      const session = await getSession(req, res, { overrideGlobalClaimValidators: () => [] });

      const userId = session.getRecipeUserId();

      const isVerified = await EmailVerification.isEmailVerified(userId, session.getTenantId());
      return res.status(200).json({ status: "OK", isVerified: isVerified });
    } catch (err) {
      console.error("check email verification error:", err);
      return res.status(500).json({ error: "Failed to check email verification status" });
    }
  }

  /**
   * Resend email verification
   */
  static async resendEmailVerification(req: Request, res: Response) {
    try {
      // This is essentially the same as sendEmailVerification
      // but with different messaging/logging for clarity
      await EmailController.sendEmailVerification(req, res);
    } catch (err) {
      console.error("resend email verification error:", err);
      return res.status(500).json({ error: "Failed to resend verification email" });
    }
  }
}
