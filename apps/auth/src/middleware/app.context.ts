import { ApplicationContext, Context } from "../init/app.context";
import type { Request, Response, NextFunction } from "express";
import * as crypto from "crypto";
import Session from "supertokens-node/recipe/session";

export async function contextMiddleware(req: Request, res: Response, next: NextFunction) {
    try {

        const session = await Session.getSession(req, res, { sessionRequired: false });

        let user = {
            userId: "anonymous",
            tenantId: "default",
            roles: [] as string[],
        };

        if (session) {
            const payload = session.getAccessTokenPayload();
            user = {
                userId: session.getUserId(),
                tenantId: payload.tenantId || "default",
                roles: (payload.roles as string[]) || [],
            };
        }

        const ctx: Context = {
            requestId: (req.headers["x-request-id"] as string) || crypto.randomUUID(),
            user,
            request: req,
            response: res,
            metadata: {},
        };

        delete req.headers["authorization"];
        ApplicationContext.run(ctx, () => next());
    } catch (err) {
        next(err);
    }
}
