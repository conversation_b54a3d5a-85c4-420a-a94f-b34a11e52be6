// routes/userRoutes.js
import { Router } from "express";
import { StaffUserController } from "../controllers/staffUserController";

export const createUserRouter = (): Router => {
  const router = Router();

  // GET all users
  router.get("/", StaffUserController.getAllUsers);

  // GET user by ID
  router.get("/:id", StaffUserController.getUserById);

  // POST create new user
  router.post("/", StaffUserController.createUser);

  // PUT update user
  router.put("/:id", StaffUserController.updateUser);

  router.post("/list", StaffUserController.listUsers);

  router.delete("/:id", StaffUserController.deleteUser);

  return router;
};
