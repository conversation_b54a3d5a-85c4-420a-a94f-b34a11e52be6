{"name": "web", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "next dev --port 3000 --turbopack", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@repo/ui": "workspace:*", "@tanstack/react-query": "^5.89.0", "axios": "^1.12.2", "next": "^15.4.2", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "supertokens-auth-react": "^0.50.0", "zod": "^3.25.76"}, "devDependencies": {"@next/eslint-plugin-next": "^15.4.6", "@repo/logger": "workspace:*", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@types/node": "^22.15.30", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "autoprefixer": "^10.4.20", "eslint": "^9.33.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "5.9.2"}}