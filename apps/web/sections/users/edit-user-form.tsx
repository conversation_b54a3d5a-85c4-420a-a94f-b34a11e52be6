import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";
import { useEditUser } from "@/api-slice/users";

type UpdateUserPayload = {
  firstName: string;
  lastName: string;
  displayName?: string;
  emailId: string;
  role: string;
  tenantId?: string;
};

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: any;
};

type EditUserFormProps = {
  onSuccess?: () => void;
  userData: User;
};

const EditUserForm: React.FC<EditUserFormProps> = ({ onSuccess, userData }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
    control,
  } = useForm<UpdateUserPayload>({
    defaultValues: {
      firstName: userData.firstName,
      lastName: userData.lastName,
      displayName: userData.displayName,
      emailId: userData.emailId,
      role: userData.role,
    },
  });

  const editUser = useEditUser();
  const { isPending } = editUser;

  const onSubmit = (data: UpdateUserPayload) => {
    const payload = {
      ...data,
    };
    editUser.mutate({ userId: userData.id, data: payload }, { onSuccess: () => onSuccess?.() });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl space-y-6">
      <h1 className="text-center font-bold">Edit User</h1>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* First Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            First Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("firstName", { required: "First name is required" })}
            placeholder="First Name"
            className="h-10"
          />
          {errors.firstName && (
            <span className="text-xs text-red-500">{errors.firstName.message}</span>
          )}
        </div>

        {/* Last Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Last Name <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("lastName", { required: "Last name is required" })}
            placeholder="Last Name"
            className="h-10"
          />
          {errors.lastName && (
            <span className="text-xs text-red-500">{errors.lastName.message}</span>
          )}
        </div>

        {/* Display Name */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">Display Name</label>
          <Input {...register("displayName")} placeholder="Display Name" className="h-10" />
        </div>

        {/* Email ID */}
        <div className="flex flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            Email ID <span className="ml-1 text-red-500">*</span>
          </label>
          <Input
            {...register("emailId", {
              required: "Email is required",
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: "Invalid email format",
              },
            })}
            placeholder="Email ID"
            className="h-10"
            disabled
          />
          {errors.emailId && <span className="text-xs text-red-500">{errors.emailId.message}</span>}
        </div>

        {/* Role */}
        <div className="flex w-full flex-col space-y-1">
          <label className="text-sm font-medium text-gray-700">
            User Role <span className="ml-1 text-red-500">*</span>
          </label>

          <Controller
            name="role"
            control={control}
            rules={{ required: "User role is required" }}
            disabled
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value || ""} disabled>
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select Role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="user">User</SelectItem>
                  <SelectItem value="manager">Manager</SelectItem>
                </SelectContent>
              </Select>
            )}
          />

          {errors.role && <span className="text-xs text-red-500">{errors.role.message}</span>}
        </div>
      </div>

      {/* Buttons */}
      <div className="flex w-full items-center justify-between">
        <Button
          type="button"
          variant="secondary"
          className="w-full px-8 md:w-auto"
          onClick={onSuccess}
        >
          Cancel
        </Button>
        <Button type="submit" className="w-full px-8 md:w-auto">
          {isPending ? "Saving..." : "Save"}
        </Button>
      </div>
    </form>
  );
};

export default EditUserForm;
