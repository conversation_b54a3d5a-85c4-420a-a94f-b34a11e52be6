import React from "react";
import { useReactTable, TableOptions, flexRender } from "@tanstack/react-table";

interface CustomVirtualListProps<TData extends object> {
  options: TableOptions<TData>;
  rowHeight?: number;
  rowGap?: number;
}

const CustomVirtualList = <TData extends object>({
  options,
  rowHeight = 48,
  rowGap = 10,
}: CustomVirtualListProps<TData>) => {
  const table = useReactTable(options);

  const headers = table.getHeaderGroups();
  const rows = table.getRowModel().rows;

  return (
    <div className="w-full">
      {/* Header */}
      <div className="sticky top-0 z-10 flex w-full bg-white text-left font-medium">
        {headers[0]?.headers.map((header) => (
          <div key={header.id} className="p-2 text-left" style={{ width: header.getSize() }}>
            {header.isPlaceholder
              ? null
              : flexRender(header.column.columnDef.header, header.getContext())}
          </div>
        ))}
      </div>

      {/* Rows */}
      <div className="relative h-[700px] overflow-y-auto">
        {rows.map((row, index) => (
          <div
            key={row.id}
            className="absolute left-0 flex items-center rounded-md bg-[#f0f7f6] shadow-sm"
            style={{
              top: index * (rowHeight + rowGap),
              height: rowHeight,
              width: "100%",
            }}
          >
            {row.getVisibleCells().map((cell) => (
              <div key={cell.id} className="truncate p-2" style={{ width: cell.column.getSize() }}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default CustomVirtualList;
