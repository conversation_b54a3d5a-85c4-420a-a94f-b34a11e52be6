"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";

import { Button } from "@repo/ui/components/button";
import { Icons } from "@repo/ui/components/icons";
import { TableColumnHeader } from "./user-table";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@repo/ui/components/dropdown-menu";

import CustomVirtualList from "./custom-virtual-list";
import React from "react";

type User = {
  id: string;
  firstName: string;
  lastName: string;
  displayName: string;
  emailId: string;
  role: string;
  isActive: boolean;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt: any;
  first_time_user: boolean;
  tenant: {
    id: string;
    tenantId: string;
    companyName: string;
    adminName: string;
    emailId: string;
    subscriptionType: string;
    contactNo: string;
    capOnUsers: number;
    address: string;
    createdAt: string;
    updatedAt: string;
    first_time_user: boolean;
  };
};

export const getColumns = (
  onEdit?: (user: User) => void,
  onToggleStatus?: (user: any) => void,
  onDelete?: (user: User) => void
): ColumnDef<User>[] => [
  {
    accessorKey: "firstName",
    header: ({ column }) => <TableColumnHeader column={column} title="First Name" />,
    cell: ({ row }) => row.getValue("firstName"),
    enableSorting: false,
    size: 200,
  },
  {
    accessorKey: "lastName",
    header: ({ column }) => <TableColumnHeader column={column} title="Last Name" />,
    cell: ({ row }) => row.getValue("lastName"),
    enableSorting: false,
    size: 200,
  },
  {
    accessorKey: "displayName",
    header: ({ column }) => <TableColumnHeader column={column} title="Display Name" />,
    cell: ({ row }) => row.getValue("displayName"),
    enableSorting: false,
    size: 300,
  },
  {
    accessorKey: "emailId",
    header: ({ column }) => <TableColumnHeader column={column} title="Email ID" />,
    cell: ({ row }) => row.getValue("emailId"),
    enableSorting: false,
    size: 300,
  },
  {
    accessorKey: "role",
    header: ({ column }) => <TableColumnHeader column={column} title="Role" />,
    cell: ({ row }) => row.getValue("role"),
    enableSorting: false,
  },
  {
    accessorKey: "isActive",
    header: ({ column }) => <TableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => (
      <span className={`font-medium ${row.original.isActive ? "text-green-600" : "text-red-600"}`}>
        {row.original.isActive ? "Active" : "Inactive"}
      </span>
    ),
    enableSorting: false,
  },
  {
    id: "actions",
    header: "Action",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <Icons.moreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onEdit?.(user)}>Edit</DropdownMenuItem>

            {/* Toggle Activate/Deactivate */}
            <DropdownMenuItem onClick={() => onToggleStatus?.(user)}>
              {user.isActive ? "Deactivate" : "Activate"}
            </DropdownMenuItem>

            {/* Delete */}
            {/* <DropdownMenuItem onClick={() => onDelete?.(user)} className="text-destructive">
              Delete
            </DropdownMenuItem> */}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

interface UsersTableProps {
  userData: User[];
  onEdit?: (user: User) => void;
  onToggleStatus?: (user: any) => void;
  onDelete?: (user: User) => void;
}

const UsersTable: React.FC<UsersTableProps> = ({ userData, onEdit, onToggleStatus, onDelete }) => {
  const options = {
    data: userData,
    columns: getColumns(onEdit, onToggleStatus, onDelete),
    getCoreRowModel: getCoreRowModel(),
  };

  const table = useReactTable(options);

  const headers = table.getHeaderGroups();
  const rows = table.getRowModel().rows;
  return (
    <div className="bg-background rounded-sm p-6">
      {/* Header */}
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" className="p-2">
            <Icons.arrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-foreground text-2xl font-semibold">User Management</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="gap-2 bg-transparent">
            <Icons.download className="h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm" className="gap-2 bg-transparent">
            <Icons.sortAsc className="h-4 w-4" />
            Sort
          </Button>
          <Button variant="outline" size="sm" className="gap-2 bg-transparent">
            <Icons.filter className="h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      {/* Table */}

      <CustomVirtualList
        options={{
          data: userData,
          columns: getColumns(onEdit, onToggleStatus, onDelete),
          getCoreRowModel: getCoreRowModel(),
        }}
      />
    </div>
  );
};

export default UsersTable;
