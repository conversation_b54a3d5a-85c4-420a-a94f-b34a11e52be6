"use client";
import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import { SubNavBar } from "@/components/sub-nav-bar";
import UsersTable from "../user-list";
import { useGetUsers, useActivateUser, useDeactivateUser, useDeleteUser } from "@/api-slice/users";
import AddUserForm from "../add-user-form";
import EditUserForm from "../edit-user-form";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import Loading from "@repo/ui/components/loading";

const UserList: React.FC = () => {
  const [openAdd, setOpenAdd] = useState(false);
  const [openEdit, setOpenEdit] = useState(false);
  const [openToggle, setOpenToggle] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);

  const [selectedUser, setSelectedUser] = useState<any | null>(null);

  const { data: userData, isLoading, error } = useGetUsers();

  // Mutations
  const activateUser = useActivateUser();
  const deactivateUser = useDeactivateUser();
  const deleteUser = useDeleteUser();

  // Handlers
  const handleEdit = (user: any) => {
    setSelectedUser(user);
    setOpenEdit(true);
  };

  const handleToggleStatus = (user: any) => {
    setSelectedUser(user);
    setOpenToggle(true);
  };

  const handleDelete = (user: any) => {
    setSelectedUser(user);
    setOpenDelete(true);
  };

  console.log("selectedUser", selectedUser);

  if (isLoading) return <Loading />;

  if (error) return <>Something went wrong</>;

  return (
    <div className="flex h-full flex-col">
      <SearchBar />

      {/* Top Bar */}
      <div className="py-2">
        <SubNavBar actionButton={[{ name: "Add User", onClick: () => setOpenAdd(true) }]} />
      </div>

      {/* Users Table */}
      <UsersTable
        userData={userData || []}
        onEdit={handleEdit}
        onToggleStatus={handleToggleStatus}
        onDelete={handleDelete}
      />

      {/* Add User Dialog */}
      <Dialog open={openAdd} onOpenChange={setOpenAdd}>
        <DialogContent className="sm:max-w-[700px]">
          <AddUserForm onSuccess={() => setOpenAdd(false)} />
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={openEdit} onOpenChange={setOpenEdit}>
        <DialogContent className="sm:max-w-[700px]">
          {selectedUser && (
            <EditUserForm userData={selectedUser} onSuccess={() => setOpenEdit(false)} />
          )}
        </DialogContent>
      </Dialog>

      {/* Toggle Status Dialog (Activate / Deactivate) */}
      <Dialog open={openToggle} onOpenChange={setOpenToggle}>
        <DialogContent className="max-w-md">
          <h2 className="text-center text-lg font-semibold">
            {selectedUser?.isActive ? "Deactivate User" : "Activate User"}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Notification will be sent to the below registered email address.
          </p>
          <div className="mt-6 flex justify-end gap-3">
            <Input value={selectedUser?.emailId} disabled />
            <Button
              onClick={() => {
                if (selectedUser?.isActive) {
                  deactivateUser.mutate(
                    { userId: selectedUser?.id, data: { isActive: false } },
                    { onSuccess: () => setOpenToggle(false) }
                  );
                } else {
                  activateUser.mutate(
                    { userId: selectedUser?.id, data: { isActive: false } },
                    { onSuccess: () => setOpenToggle(false) }
                  );
                }
              }}
              disabled={activateUser.isPending || deactivateUser.isPending}
            >
              {activateUser.isPending || deactivateUser.isPending ? "confirming" : "Confirm"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={openDelete} onOpenChange={setOpenDelete}>
        <DialogContent className="max-w-md">
          <h2 className="text-center text-lg font-semibold text-red-600">Delete User</h2>
          <p className="mt-2 text-center text-sm text-gray-600">Are you sure you want to delete?</p>
          <div className="mt-6 flex justify-end gap-3">
            <Button variant="secondary" onClick={() => setOpenDelete(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() =>
                deleteUser.mutate(selectedUser.id, { onSuccess: () => setOpenDelete(false) })
              }
              disabled={deleteUser.isPending}
            >
              {deleteUser.isPending ? "Deleting..." : "Delete"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserList;
