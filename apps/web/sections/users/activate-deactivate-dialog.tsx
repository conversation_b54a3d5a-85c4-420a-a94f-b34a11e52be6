"use client";
import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  DialogTitle,
} from "@repo/ui/components/dialog";
import { Button } from "@repo/ui/components/button";
import { useCreateUser } from "@/api-slice/users";

type ActivateDeactivateUserDialogProps = {
  open: boolean;
  onClose: () => void;
  userId: string;
  onSuccess?: () => void;
};

const ActivateDeactivateUserDialog: React.FC<ActivateDeactivateUserDialogProps> = ({
  open,
  onClose,
  userId,
  onSuccess,
}) => {
  const deactivateUser = useCreateUser();

  const handleConfirm = () => {
    // deactivateUser.mutate(
    //   { userId },
    //   {
    //     onSuccess: () => {
    //       onSuccess?.();
    //       onClose();
    //     },
    //   }
    // );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Deactivate User</DialogTitle>
        </DialogHeader>
        <p className="text-sm text-gray-600">Are you sure you want to deactivate this user?</p>
        <DialogFooter>
          <Button variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={deactivateUser.isPending}>
            {deactivateUser.isPending ? "Deactivating..." : "Deactivate"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ActivateDeactivateUserDialog;
