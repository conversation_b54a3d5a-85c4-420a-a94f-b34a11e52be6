"use client";
import type React from "react";
import { useForm } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { useCreateUser } from "@/api-slice/users";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@repo/ui/components/select";

type CreateUserPayload = {
  firstName: string;
  lastName: string;
  emailId: string;
  role: string;
};

const config = [
  {
    name: "First Name",
    key: "firstName",
    type: "input",
    validation: { required: "First name is required" },
  },
  {
    name: "Last Name",
    key: "lastName",
    type: "input",
    validation: { required: "Last name is required" },
  },
  {
    name: "Display Name",
    key: "displayName",
    type: "input",
    validation: { required: false },
  },
  {
    name: "Email ID",
    key: "emailId",
    type: "input",
    validation: {
      required: "Email is required",
      pattern: {
        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        message: "Invalid email format",
      },
    },
  },
  {
    name: "User Role",
    key: "role",
    type: "select",
    validation: { required: "User role is required" },
    options: [{ value: "admin", label: "Admin" }],
  },
];

type AddUserFormProps = {
  onSuccess?: () => void;
};

const AddUserForm: React.FC<AddUserFormProps> = ({ onSuccess }) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CreateUserPayload>();

  const createUser = useCreateUser();

  const { isPending } = createUser;

  const onSubmit = (data: CreateUserPayload) => {
    const payload = {
      ...data,
    };
    createUser.mutate(payload, {
      onSuccess: () => {
        onSuccess?.();
      },
    });
  };

  const renderField = (field: any) => {
    switch (field.type) {
      case "input":
        return (
          <Input
            {...register(field.key, field.validation)}
            placeholder={field.name}
            className="h-10"
          />
        );
      case "select":
        return (
          <Select
            onValueChange={(val) => setValue(field.key, val, { shouldValidate: true })}
            defaultValue={watch(field.key)}
          >
            <SelectTrigger className="h-10">
              <SelectValue placeholder={`Select ${field.name}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      default:
        return null;
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="max-w-4xl space-y-6">
      <h1 className="text-center">User Registry</h1>
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {config.map((field) => (
          <div key={field.key} className="flex flex-col space-y-1">
            <label className="text-sm font-medium text-gray-700">
              {field.name}
              {field.validation?.required && <span className="ml-1 text-red-500">*</span>}
            </label>
            {renderField(field)}
            {errors[field.key] && (
              <span className="text-xs text-red-500">{errors[field.key]?.message as string}</span>
            )}
          </div>
        ))}
      </div>

      <Button type="submit" className="w-full px-8 md:w-auto">
        {isPending ? "Registering..." : "Register User"}
      </Button>
    </form>
  );
};

export default AddUserForm;
