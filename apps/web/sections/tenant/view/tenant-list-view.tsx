"use client";
import React, { useState } from "react";
import { SearchBar } from "@/components/search-bar";
import TenantTable from "../tenant-list";
import { SubNavBar } from "@/components/sub-nav-bar";
import { Dialog, DialogContent } from "@repo/ui/components/dialog";
import AddTenantFormView from "./add-tenant-form-view";
import AddTenantForm from "../add-tenant-form";
import { useGetTenants } from "@/api-slice/tenant";

const TenantListView: React.FC = () => {
  const [open, setOpen] = useState(false);
  const { data: tenantData, isLoading, error } = useGetTenants();

  if (isLoading) return <div>Loading...</div>;
  if (error) {
    return <div>Something went wrong...</div>;
  }
  console.log("tenantData", tenantData);
  return (
    <div className="h-[90vh] w-full bg-[#f0f7f6] p-10">
      <SearchBar />

      <Dialog open={open} onOpenChange={setOpen}>
        <div className="py-3">
          <SubNavBar actionButton={[{ name: "Add Tenant", onClick: () => setOpen(true) }]} />
        </div>
        <TenantTable tenantData={tenantData ?? []} />

        <DialogContent className="sm:max-w-[700px]">
          <AddTenantForm />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TenantListView;
