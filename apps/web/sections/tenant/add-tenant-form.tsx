"use client";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Input } from "@repo/ui/components/input";
import { Button } from "@repo/ui/components/button";
import { useCreateTenant } from "@/api-slice/tenant";

type CreateTenantPayload = {
  companyName: string;
  adminName: string;
  emailId: string;
  subscriptionType: string;
  contactNo: string;
  capOnUsers: number;
  address: string;
};

const AddTenantForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<CreateTenantPayload>({
    defaultValues: { capOnUsers: 1 },
  });

  const userCapacity = watch("capOnUsers");

  const createUser = useCreateTenant();

  const { isPending } = createUser;

  const onSubmit = (data: CreateTenantPayload) => {
    const payload = {
      ...data,
    };
    createUser.mutate(payload);
  };

  // Increment / Decrement
  const increment = () => setValue("capOnUsers", (userCapacity || 0) + 1);
  const decrement = () => setValue("capOnUsers", Math.max(1, (userCapacity || 1) - 1));

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-6">
      <h1 className="text-center text-xl font-semibold">Tenant Registry</h1>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Company Name */}
        <div className="flex flex-col space-y-1">
          <Input
            {...register("companyName", { required: "Company name is required" })}
            placeholder="Company Name"
            className="h-10"
          />
          {errors.companyName && (
            <span className="text-xs text-red-500">{errors.companyName.message}</span>
          )}
        </div>

        {/* Admin Name */}
        <div className="flex flex-col space-y-1">
          <Input
            {...register("adminName", { required: "Admin name is required" })}
            placeholder="Admin Name"
            className="h-10"
          />
          {errors.adminName && (
            <span className="text-xs text-red-500">{errors.adminName.message}</span>
          )}
        </div>

        {/* Admin Email */}
        <div className="flex flex-col space-y-1">
          <Input
            {...register("emailId", {
              required: "Admin email is required",
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: "Invalid email format",
              },
            })}
            placeholder="Admin Email"
            className="h-10"
          />
          {errors.emailId && <span className="text-xs text-red-500">{errors.emailId.message}</span>}
        </div>

        {/* Subscription Type - Dropdown */}
        <div className="flex flex-col space-y-1">
          <select
            {...register("subscriptionType", { required: "Subscription type is required" })}
            className="border-input bg-background h-10 rounded-md border px-3 text-sm"
          >
            <option value="">Select Subscription</option>
            <option value="basic">Basic</option>
            <option value="standard">Standard</option>
            <option value="premium">Premium</option>
          </select>
          {errors.subscriptionType && (
            <span className="text-xs text-red-500">{errors.subscriptionType.message}</span>
          )}
        </div>

        {/* Contact Number */}
        <div className="flex flex-col space-y-1">
          <Input
            {...register("contactNo", {
              required: "Contact number is required",
              pattern: {
                value: /^[0-9]{10}$/,
                message: "Contact number must be 10 digits",
              },
            })}
            placeholder="Contact Number"
            className="h-10"
          />
          {errors.contactNo && (
            <span className="text-xs text-red-500">{errors.contactNo.message}</span>
          )}
        </div>

        {/* Address */}
        <div className="flex flex-col space-y-1">
          <Input
            {...register("address", { required: "Address is required" })}
            placeholder="Address"
            className="h-10"
          />
          {errors.address && <span className="text-xs text-red-500">{errors.address.message}</span>}
        </div>

        {/* User Capacity with + / - */}
        <div className="flex flex-col space-y-1">
          <div className="flex items-center justify-between">
            <p>User Cap</p>
            <div className="flex items-center gap-2">
              <Button type="button" variant="outline" onClick={decrement}>
                -
              </Button>
              <Input
                type="number"
                {...register("capOnUsers", {
                  required: "User capacity is required",
                  min: { value: 1, message: "User capacity must be at least 1" },
                })}
                value={userCapacity}
                readOnly
                className="h-10 w-full text-center"
              />
              <Button type="button" variant="outline" onClick={increment}>
                +
              </Button>
            </div>
          </div>
          {errors.capOnUsers && (
            <span className="text-xs text-red-500">{errors.capOnUsers.message}</span>
          )}
        </div>
      </div>

      <Button type="submit" className="w-full px-8 md:w-auto">
        {isPending ? "Registering..." : "Register User"}
      </Button>
    </form>
  );
};

export default AddTenantForm;
