import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import instance from '../lib/instance';

export type CreateUserPayload = {
  firstName: string
  lastName: string
  emailId: string
  role: string
}

type EditUserPayload = {
  userId: string;
  data: CreateUserPayload;
};

type ActivateDeactivate = {
  userId: string;
  data : {
    isActive : boolean
  }
}


// GET request with useQuery
export function useUserData(userId: string) {
  return useQuery({
    queryKey: ['users', userId],
    queryFn: async () => {
      const response = await instance.get(`/users/${userId}`);
      return response.data;
    },
  });
}

export function useGetUsers() {
  return useQuery({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await instance.get(`/users`);
      console.log("useGetUsers query", response)
      return response.data;
    },
  });
}

// POST request with useMutation
export function useCreateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userData: CreateUserPayload) => {
      const response = await instance.post('/users', userData);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch users queries
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
}

export function useEditUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ userId, data }: EditUserPayload) => {
      const response = await instance.put(`/users/${userId}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

// ACTIVATE user
export function useActivateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({userId, data} : ActivateDeactivate) => {
      const response = await instance.patch(`/users/${userId}/activate`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
}

// DEACTIVATE user
export function useDeactivateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({userId, data} : ActivateDeactivate) => {
      const response = await instance.patch(`/users/${userId}/deactivate`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
}

// DELETE user
export function useDeleteUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userId: string) => {
      const response = await instance.delete(`/users/${userId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
  });
}