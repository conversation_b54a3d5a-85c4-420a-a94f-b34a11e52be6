import { plainToInstance } from "class-transformer";
import { validate, ValidationError as ClassValidationError } from "class-validator";
import { ServiceError } from "../service/$base.service";


export class Dto {

  public static transform<T>(dtoClass: new () => T, data: object, config: object|undefined): T {
    return plainToInstance(dtoClass, data, {
      enableImplicitConversion: false,
      excludeExtraneousValues: true,
      ...(config || {}),
    });
  }


  public static async validateAndTransform<T extends Object>(
    dtoClass: new () => T,
    payload: object,
    config?: object
  ): Promise<T> {
    return await Dto.validate(dtoClass, payload, config);
  }


  public static async validate<T extends object>(
    dtoClass: new () => T,
    payload: object,
    config?: object
  ): Promise<T> {
    const instance = plainToInstance(dtoClass, payload, {
      enableImplicitConversion: false,
      excludeExtraneousValues: true,
      ...(config || {}),
    });

    const errors: ClassValidationError[] = await validate(instance, config);

    if (errors.length > 0) {
      const errorString = errors
        .map((e) => Object.values(e.constraints ?? {}).join(", "))
        .filter(Boolean)
        .join("; ");

      throw new ServiceError(
        errorString,
        "VALIDATION_ERROR",
        400
      );
    }

    return instance;
  }
}
