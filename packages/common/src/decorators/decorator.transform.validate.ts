import "reflect-metadata";
import { StatusCodes } from "http-status-codes";
import { Dto } from "../$abstract/$base.decorator";

interface BluePrint<TInput extends object = any, TOutput = any> {
  /** Optional DTO class for input */
  param_blueprint?: new () => TInput;

  /** Optional DTO class for output */
  response_blueprint?: new () => TOutput;
}

interface Config {
  /** Whether input should be validated or just transformed */
  validate?: boolean;
}

export function Transform<TInput extends object = any, TOutput = any>(
  parameter: BluePrint<TInput, TOutput> = {},
  config: Config = {}
): MethodDecorator {
  return function (
    _target: any,
    _propertyKey: string | symbol,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (arg: any) {
      try {
        let transformedInput: TInput | any = arg;

        // 🔹 Only transform if param_blueprint is a class/DTO
        if (parameter.param_blueprint && isDto(parameter.param_blueprint)) {
          transformedInput = config.validate
            ? await Dto.validateAndTransform(parameter.param_blueprint, arg, {
                lang: arg?.headers?.x_header?.lang,
              })
            : Dto.transform(parameter.param_blueprint, arg, {
                lang: arg?.headers?.x_header?.lang,
              });
        }

        // 🔹 Call original method
        const result: TOutput | void = await originalMethod.apply(this, [
          transformedInput,
        ]);

        // 🔹 Transform output if response blueprint provided
        if (parameter.response_blueprint && result !== undefined) {
          return Object.freeze({
            success: true,
            statusCode: StatusCodes.OK,
            data: result != null
              ? Dto.transform(parameter.response_blueprint, result, {
                  lang: arg?.headers?.x_header?.lang,
                })
              : null,
          });
        }

        // 🔹 Standard success response
        return Object.freeze({
          success: true,
          statusCode: StatusCodes.OK,
          data: result ?? null,
        });
      } catch (err: any) {
        // 🔹 Standard error response
        return Object.freeze({
          success: false,
          statusCode: err.statusCode ?? StatusCodes.INTERNAL_SERVER_ERROR,
          code: err.code ?? "INTERNAL_ERROR",
          message: err.message ?? "Unexpected error",
          error: err,
        });
      }
    };
  };
}

/**
 * Detect if blueprint is a DTO class using metadata or ES6 class heuristics
 */
function isDto(fn: any): boolean {
  if (typeof fn !== "function") return false;

  // ✅ Detect ES6 class
  const isClass = /^class\s/.test(Function.prototype.toString.call(fn));
  if (isClass) return true;

  // ✅ Detect if TypeScript design:type metadata is present (decorated DTO)
  try {
    return (
      Reflect.hasMetadata("design:type", fn) ||
      Reflect.hasMetadata("design:paramtypes", fn) ||
      Reflect.hasMetadata("design:returntype", fn)
    );
  } catch {
    return false;
  }
}
