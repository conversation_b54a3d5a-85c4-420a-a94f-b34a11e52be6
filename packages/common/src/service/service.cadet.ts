interface Project {
  id: string;
  name: string;
  description?: string;
  createdAt: number;
  tenantId?: string;
  ownerId?: string;
}

interface CreateProjectPayload {
  name: string;
  description?: string;
}

interface GetProjectPayload {
  id: string;
}

const projectStore = new Map<string, Project>();

export class ProjectService extends Service.Base<any, any> {
  constructor() {
    super("ProjectService");
  }


  async create(
    params: Service.Params<CreateProjectPayload>
  ): Promise<Service.Response<Project> | Service.ErrorResponse<Project>> {

    return this.execute(params, async (params: Service.Params<CreateProjectPayload>) => {

      const { name, description } = params.payload;

      const project: Project = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 6)}`,
        name,
        description,
        createdAt: Date.now(),
        tenantId: params.tenantId,
        ownerId: params.userId
      };

      projectStore.set(project.id, project);
      return project;
    });
  }


  async get(
    params: Service.Params<GetProjectPayload>
  ): Promise<Service.Response<Project> | Service.ErrorResponse<Project>> {
    return this.execute(params, async (params: Service.Params<GetProjectPayload>) => {

      const project = projectStore.get(params.payload.id);
      if (!project) {
        throw new Error(`Project with ID ${params.payload.id} not found`);
      }
      return project;
    });
  }
}
