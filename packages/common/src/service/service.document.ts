export class DocumentService extends Service.Base<any, any> {
    constructor() {
        super("DocumentService");
    }

    async create(payload: any): Promise<Service.Response<any> | Service.ErrorResponse<any>> {
        return this.execute({ payload, requestId: "", context: {}, }, this.createDocumentEntry);
    }

    private async createDocumentEntry({ context:{}, payload, requestId }: Service.Params): Promise<any> {
        return  {};
    }
}