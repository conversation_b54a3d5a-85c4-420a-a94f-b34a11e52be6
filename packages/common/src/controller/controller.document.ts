import { Response, Request } from "express";
import { DocumentService } from "../service/service.document";
import { Transform } from "../decorators/decorator.transform.validate";

export class DocumentController extends Controller.Base{
 

      @Transform({param_blueprint: , response_blueprint: })
      static async create(req: Request, res: Response): Promise<Response> {
        const service = new DocumentService();
        return res.status(501).json(service.create(req.body));
      }


      
}