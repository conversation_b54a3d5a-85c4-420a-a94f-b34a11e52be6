{
  "compilerOptions": {
   
    "target": "ES2022",                     // Modern JS output
    "lib": ["ES2022"],                      // Latest standard library
    "module": "NodeNext",                   // For modern Node (ESM + CJS interop)
    "moduleResolution": "NodeNext",         // Best for Node with "type": "module"

    /* Output */
    "outDir": "dist",                       // Emit compiled files here
    "rootDir": "src",                       // Source folder
    "declaration": true,                    // Generate .d.ts files
    "declarationMap": true,                 // Useful for debugging libraries
    "sourceMap": false,                     // Disable for production (enable in dev)
    "removeComments": true,                 // Clean output
    "noEmitOnError": true,                  // Don’t emit if there are errors

    /* Strictness */
    "strict": true,                         // Enables all strict checks
    "noUnusedLocals": true,                 // Catch dead code
    "noUnusedParameters": true,
    "noImplicitReturns": true,              // Ensure all code paths return
    "noFallthroughCasesInSwitch": true,     // Safer switch statements

    /* Interop */
    "esModuleInterop": true,                // For compatibility with CommonJS
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,              // Allow importing JSON
    "isolatedModules": true,                // Ensures each file can be transpiled independently

    /* Performance */
    "skipLibCheck": true,                   // Faster builds (safe in prod)
    "forceConsistentCasingInFileNames": true,
    
    /* Decorator support */
    "experimentalDecorators": false,
    "emitDecoratorMetadata": false,
    "useDefineForClassFields": true
  },
  "include": ["src", "src/$abstract"],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}
