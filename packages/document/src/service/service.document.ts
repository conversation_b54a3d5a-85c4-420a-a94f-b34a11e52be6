import { BaseService } from "@repo/abstract";
import type { Service } from "@repo/types";
import { db } from "../db"; // your DB client

// ---- Payload/result types ----
interface DocumentPayload {
  userId: string;
  fileName: string;
  filePath: string;
  fileType: string;
}

interface DocumentResult {
  documentId: string;
  userId: string;
  fileName: string;
  filePath: string;
  fileType: string;
  uploadedAt: Date;
}

export class DocumentService extends BaseService<DocumentPayload, DocumentResult> {
  constructor() {
    super("DocumentService");
  }

  async markDocumentUploaded(params: Service.Params<DocumentPayload>) {
    return this.execute(
      {
        ...params,
        preHooks: params.preHooks ?? [],
        postHooks: params.postHooks ?? [],
        onError: params.onError ?? [],
      },
      (p) => this.operation(p)
    );
  }

  protected async operation(params: Service.Params<DocumentPayload>): Promise<DocumentResult> {
    const { payload } = params;

    const record = await db.document.create({
      data: {
        userId: payload.userId,
        fileName: payload.fileName,
        filePath: payload.filePath,
        fileType: payload.fileType,
        uploadedAt: new Date(),
      },
    });

    return {
      documentId: record.id,
      userId: record.userId,
      fileName: record.fileName,
      filePath: record.filePath,
      fileType: record.fileType,
      uploadedAt: record.uploadedAt,
    };
  }
}
