import { MultipartUploader, uploadAdapters } from "@cadetlabs/network-store";
import { DocumentService } from "../service/service.document";

export class DocumentController {
    async create(request:Express.Request, response:Express.Response){

        // Choose adapter, e.g. AZURE
        const uploadFn = uploadAdapters.AZURE(/* pass connectionString, containerName if needed */);
        const documentService = new DocumentService();

        const uploader = new MultipartUploader({
            onAfterUpload: async (fileMeta) => {
                // Create document entry in DB
                return await documentService.create({
                    name: fileMeta.originalName,
                    blobName: fileMeta.blobName,
                    uri: fileMeta.uri,
                    mimetype: fileMeta.mimetype,
                    encoding: fileMeta.encoding,
                    size: fileMeta.size,
                    // add other metadata fields as needed
                });
            }
        }, uploadFn);

        uploader.handler(request, response);
    }

    async update(){

    }
}