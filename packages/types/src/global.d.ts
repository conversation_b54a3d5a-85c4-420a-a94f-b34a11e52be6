import type { ServiceError } from "@cadetlabs/types";

declare global {

  namespace NodeJS {
    interface SuperTokensMetadata {
      firstName: string;
      lastName: string;
      email: string;
      tenantId: string;
      userId: string;
      role: string;
      firstTimeUser: boolean;
    }
  }

  namespace Express {
    interface Request {
      user: SuperTokensMetadata;
    }
  }

  namespace Controller {
    var Base: typeof _BaseController;
  }

  namespace Service {
    interface Response<TResult = any> {
      readonly success: boolean;
      readonly code: string;
      readonly message: string;
      readonly result?: TResult;
      readonly error?: ServiceError;
      readonly executionTime: number;
      readonly context: Record<string, any>;
    }

    export interface Params<TPayload = any> {
      readonly preHooks?: Array<PreExecuteHook<TPayload>>;
      readonly postHooks?: Array<PostExecuteHook<TPayload>>;
      readonly onError?: Array<(error: ServiceError, request: Params<TPayload>) => Promise<void> | void>;
      readonly requestId: string;
      readonly userId?: string;
      readonly tenantId?: string;
      readonly payload: TPayload;
      readonly metadata?: Record<string, any>;
      readonly context: Record<string, any>;
    }

    export interface ErrorResponse<TResult = any> {
      readonly success: false;
      readonly code: string;
      readonly message: string;
      readonly error: ServiceError;
      readonly executionTime: number;
      readonly result?: TResult; // optional, usually absent,
      context?: Record<string, any>
    }

    type PreExecuteHook<TPayload> = (
      params: Service.Params<TPayload>
    ) => Promise<void> | void;

    type PostExecuteHook<TPayload, TResult> = (
      params: Service.Params<TPayload>,
      response: Service.Response<TResult>
    ) => Promise<void> | void;
    type ErrorHook = (
      error: ServiceError,
      request: ServiceRequest
    ) => Promise<void> | void;
    type Operation<TPayload, TResult> = (
      params: Service.Params<TPayload>
    ) => Promise<TResult>;

  }
}

export { };