import { Readable } from "stream";
import busboy from "busboy";
import { azureUploadAdapter } from "./adapters/network-store.adapter";

export const uploadAdapters = {
  AZURE: azureUploadAdapter,
};

type Options = {
  onAfterUpload: Function
}
export class MultipartUploader {
  
  private options;
  private uploadFn;

  constructor(options:Options, uploadFn) {
    this.options = options;
    this.uploadFn = uploadFn;
  }

  handler(req, res) {
    const bb = busboy({ headers: req.headers });
    const uploadedFiles = [];
    let metadata = null;

    bb.on("field", (fieldname, val) => {
      if (fieldname === "metadata") {
        try {
          metadata = JSON.parse(val);
        } catch {
          res.status(400).json({ error: "Invalid metadata JSON" });
        }
      }
    });

    bb.on("file", async (fieldname, file, filename, encoding, mimetype) => {
      if (!metadata) {
        file.resume();
        uploadedFiles.push(this.createFailedFile(fieldname, filename, mimetype, encoding, "Metadata not provided"));
        return;
      }

      const fileMeta = metadata.find(m => m.originalName === filename);
      if (!fileMeta) {
        file.resume();
        uploadedFiles.push(this.createFailedFile(fieldname, filename, mimetype, encoding, "No matching metadata"));
        return;
      }

      try {
        const result = await this.uploadFn({
          fieldname,
          filename,
          mimetype,
          encoding,
          size: fileMeta.size,
          stream: file
        });

        let dbRecord = null;
        if (this.options.onAfterUpload) {
          dbRecord = await this.options.onAfterUpload({
            ...fileMeta,
            fieldname,
            blobName: result.blobName,
            uri: result.uri,
          });
        }

        uploadedFiles.push({
          ...fileMeta,
          fieldname,
          blobName: result.blobName,
          mimetype,
          encoding,
          uri: result.uri,
          extraFields: {},
          status: "ok",
          dbRecord,
        });
      } catch (err) {
        uploadedFiles.push(this.createFailedFile(fieldname, filename, mimetype, encoding, err.message));
      }
    });

    bb.on("close", () => res.json({ uploadedFiles }));
    req.pipe(bb);
  }

  createFailedFile(fieldname, filename, mimetype, encoding, error) {
    return {
      fieldname,
      originalName: filename,
      blobName: "",
      mimetype,
      encoding,
      uri: "",
      extraFields: {},
      status: "failed",
      error,
      size: 0,
      contentType: ""
    };
  }
}