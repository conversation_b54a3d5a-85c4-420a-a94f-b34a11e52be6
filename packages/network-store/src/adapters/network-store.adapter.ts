import { BlobServiceClient } from '@azure/storage-blob';
import { Readable } from 'stream';

interface FileUploadInfo {
  fieldname: string;
  filename: string;
  mimetype: string;
  encoding: string;
  size?: number;
  stream: Readable;
}

interface UploadResult {
  blobName: string;
  uri: string;
  contentType: string;
  size?: number | undefined;
}

export function azureUploadAdapter(connectionString: string, containerName: string){
  const blobService = BlobServiceClient.fromConnectionString(connectionString);
  const containerClient = blobService.getContainerClient(containerName);

  return async ({ filename, mimetype, size, stream }: FileUploadInfo): Promise<UploadResult> => {
    const blobName = `${Date.now()}_${Math.random().toString(36).substring(2, 8)}_${filename}`;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);
    
    const MB = 1024 * 1024;
    const GB = 1024 * MB;
    
    if (size && size >= GB) {
      await blockBlobClient.uploadStream(stream, 16 * MB, 8, {
        blobHTTPHeaders: { blobContentType: mimetype }
      });
    } else if (size && size >= 100 * MB) {
      await blockBlobClient.uploadStream(stream, 8 * MB, 6, {
        blobHTTPHeaders: { blobContentType: mimetype }
      });
    } else if (size && size >= 10 * MB) {
      await blockBlobClient.uploadStream(stream, 4 * MB, 4, {
        blobHTTPHeaders: { blobContentType: mimetype }
      });
    } else {
      await blockBlobClient.uploadStream(stream, 1 * MB, 2, {
        blobHTTPHeaders: { blobContentType: mimetype }
      });
    }

    return {
      blobName,
      uri: blockBlobClient.url,
      contentType: mimetype,
      size
    };
  };
};