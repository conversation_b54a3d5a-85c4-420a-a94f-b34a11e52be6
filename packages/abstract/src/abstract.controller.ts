import { Request, Response, Router } from 'express';

export abstract class BaseController {

  constructor() {
   
    throw new Error("BaseController cannot be instantiated. Use static methods instead.");
  }

  static async create(req: Request, res: Response): Promise<Response> {
    return res.status(501).json({
      success: false,
      message: "Create method not implemented"
    });
  }

  static async get(req: Request, res: Response): Promise<Response> {
    return res.status(501).json({
      success: false,
      message: "Get method not implemented"
    });
  }

  static async find(req: Request, res: Response): Promise<Response> {
    return res.status(501).json({
      success: false,
      message: "Find method not implemented"
    });
  }

  static async update(req: Request, res: Response): Promise<Response> {
    return res.status(501).json({
      success: false,
      message: "Update method not implemented"
    });
  }

  static async delete(req: Request, res: Response): Promise<Response> {
    return res.status(501).json({
      success: false,
      message: "Delete method not implemented"
    });
  }

  static getRouter(): Router {
    const router = Router();

    router.post('/', this.create);
    router.get('/', this.find);
    router.get('/:id', this.get);
    router.put('/:id', this.update);
    router.delete('/:id', this.delete);
    
    return router;
  }
}


// Example usage:
/*
import BaseController from './BaseController';
import { Request, Response } from 'express';

interface User {
  id: number;
  name: string;
  email?: string;
}

class UserController extends BaseController {
  static async create(req: Request, res: Response): Promise<Response> {
    // Implementation for creating a user
    const userData: Partial<User> = req.body;
    
    return res.status(201).json({
      success: true,
      message: "User created successfully",
      data: { id: 1, ...userData }
    });
  }

  static async get(req: Request, res: Response): Promise<Response> {
    // Implementation for getting a single user
    const userId = parseInt(req.params.id);
    
    return res.status(200).json({
      success: true,
      data: { 
        id: userId, 
        name: "John Doe", 
        email: "<EMAIL>" 
      } as User
    });
  }

  static async find(req: Request, res: Response): Promise<Response> {
    // Implementation for finding/listing users
    const users: User[] = [
      { id: 1, name: "John Doe", email: "<EMAIL>" },
      { id: 2, name: "Jane Smith", email: "<EMAIL>" }
    ];

    return res.status(200).json({
      success: true,
      data: users,
      total: users.length
    });
  }

  // update and delete methods will return "method not implemented" 
  // unless specifically overridden as static methods
}

// Usage in routes (no instantiation needed):
app.use('/users', UserController.getRouter());
app.use('/products', BaseController.getRouter()); // All methods return "not implemented"

// Individual route usage is still possible:
app.post('/custom-users', UserController.create);
app.get('/custom-users/:id', UserController.get);

// Router configuration example:
/*
// app.js
import express from 'express';
import UserController from './controllers/UserController';
import ProductController from './controllers/ProductController';

const app = express();

// Auto-configure all CRUD routes
app.use('/api/users', UserController.getRouter());
app.use('/api/products', ProductController.getRouter());

// This creates the following routes automatically:
// POST   /api/users      -> UserController.create
// GET    /api/users      -> UserController.find
// GET    /api/users/:id  -> UserController.get
// PUT    /api/users/:id  -> UserController.update
// DELETE /api/users/:id  -> UserController.delete
*/